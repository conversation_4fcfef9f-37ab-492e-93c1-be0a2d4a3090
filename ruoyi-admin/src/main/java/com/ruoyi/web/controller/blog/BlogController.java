package com.ruoyi.web.controller.blog;

import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.uuid.IdUtils;
import com.ruoyi.system.domain.BlogComment;
import com.ruoyi.system.domain.BlogForumPost;
import com.ruoyi.system.domain.BlogModule;
import com.ruoyi.system.domain.BlogTestPoint;
import com.ruoyi.system.domain.vo.BlogForumPostDto;
import com.ruoyi.system.domain.vo.TestPointDto;
import com.ruoyi.system.service.IBlogCommentService;
import com.ruoyi.system.service.IBlogForumPostService;
import com.ruoyi.system.service.IBlogModuleService;
import com.ruoyi.system.service.IBlogTestPointService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * 博客控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/blog")
public class BlogController extends BaseController
{
    @Autowired
    private IBlogModuleService blogModuleService;

    @Autowired
    private IBlogTestPointService blogTestPointService;

    @Autowired
    private IBlogForumPostService blogForumPostService;

    @Autowired
    private IBlogCommentService blogCommentService;

    /**
     * 获取测试模块列表
     */
    @GetMapping("/modules")
    public AjaxResult getModules()
    {
        BlogModule blogModule = new BlogModule();
        blogModule.setStatus("0"); // 只查询正常状态的模块
        List<BlogModule> list = blogModuleService.selectBlogModuleList(blogModule);
        System.out.println(JSONObject.toJSONString(list));
        return AjaxResult.success(list);
    }

    /**
     * 获取测试模块详情
     */
    @GetMapping("/modules/{moduleId}")
    public AjaxResult getModule(@PathVariable("moduleId") String moduleId)
    {
        return AjaxResult.success(blogModuleService.selectBlogModuleByModuleId(moduleId));
    }

    /**
     * 新增测试模块
     */
    @PostMapping("/modules/add")
    public AjaxResult addModule(@RequestBody BlogModule blogModule)
    {
        blogModule.setCreateBy(getUsername());
        return toAjax(blogModuleService.insertBlogModule(blogModule));
    }

    /**
     * 修改测试模块
     */
    @PutMapping("/modules")
    public AjaxResult updateModule(@RequestBody BlogModule blogModule)
    {
        blogModule.setUpdateBy(getUsername());
        return toAjax(blogModuleService.updateBlogModule(blogModule));
    }

    /**
     * 删除测试模块
     */
    @DeleteMapping("/modules/{moduleId}")
    public AjaxResult removeModule(@PathVariable String moduleId)
    {
        return toAjax(blogModuleService.deleteBlogModuleByModuleId(moduleId));
    }

    /**
     * 获取测试要点列表
     */
    @GetMapping("/testPoints")
    public AjaxResult getTestPoints(BlogTestPoint blogTestPoint)
    {
        blogTestPoint.setStatus("0"); // 只查询正常状态的测试要点
        List<BlogTestPoint> list = blogTestPointService.selectBlogTestPointList(blogTestPoint);
        return AjaxResult.success(list);
    }

    /**
     * 更新测试要点描述及置顶状态
     */
    @PutMapping("/testPoints/{testPointId}")
    public AjaxResult getTestPoints(@RequestBody TestPointDto testPointDto)
    {
        System.out.println(JSONObject.toJSONString(testPointDto));
        BlogTestPoint blogTestPoint = new BlogTestPoint();
        blogTestPoint.setTestPointId(testPointDto.getTestPointId());
        blogTestPoint.setModuleId(testPointDto.getModuleId());
        blogTestPoint.setIsPinned(testPointDto.getIsPinned());
        return AjaxResult.success(blogTestPointService.updateBlogTestPoint(blogTestPoint));
    }

    /**
     * 获取测试要点详情
     */
    @GetMapping("/forumPosts/{testPointId}")
    public AjaxResult getTestPoint(@PathVariable("testPointId") String testPointId)
    {
        return AjaxResult.success(blogTestPointService.selectBlogTestPointByTestPointId(testPointId));
    }

    /**
     * 新增测试要点
     */
    @PostMapping("/testPoints/add")
    public AjaxResult addTestPoint(@RequestBody BlogTestPoint blogTestPoint)
    {
        blogTestPoint.setCreateBy(getUsername());
        return toAjax(blogTestPointService.insertBlogTestPoint(blogTestPoint));
    }

    /**
     * 修改测试要点
     */
    @PostMapping("/testPoints/update")
    public AjaxResult updateTestPoint(@RequestBody BlogTestPoint blogTestPoint)
    {
        System.out.println(JSONObject.toJSONString(blogTestPoint));
        System.out.println(blogTestPoint.getTestPointId());
        BlogTestPoint blogTestPoint1 = blogTestPointService.selectBlogTestPointByTestPointIdAndCreateBy(blogTestPoint.getTestPointId(), getUsername());

        blogTestPoint.setUpdateBy(getUsername());
        return toAjax(blogTestPointService.updateBlogTestPoint(blogTestPoint));
    }

    /**
     * 删除测试要点
     */
    @DeleteMapping("/testPoints/{testPointId}")
    public AjaxResult removeTestPoint(@PathVariable String testPointId)
    {
        return toAjax(blogTestPointService.deleteBlogTestPointByTestPointId(testPointId));
    }

    /**
     * 切换测试要点置顶状态
     */
    @PutMapping("/testPoints/{testPointId}/pin")
    public AjaxResult toggleTestPointPin(@PathVariable String testPointId, @RequestBody Boolean isPinned)
    {
        return toAjax(blogTestPointService.toggleTestPointPin(testPointId, isPinned ? 1 : 0));
    }

    /**
     * 获取论坛帖子列表
     */
    @GetMapping("/forumPosts")
    public AjaxResult getForumPosts(@RequestParam String testPointId)
    {
        String userId = SecurityUtils.getUserId().toString();
        List<BlogForumPost> posts = blogForumPostService.selectBlogForumPostsByTestPointId(
                testPointId, userId);

        List<BlogForumPostDto> blogForumPostDtos = new ArrayList<>();
        // 获取每个帖子的评论
        for (BlogForumPost post : posts) {
            BlogForumPostDto blogForumPostDto = new BlogForumPostDto();
            blogForumPostDto.setPostId(post.getPostId());
            blogForumPostDto.setContent(post.getContent());
            blogForumPostDto.setAuthor(post.getAuthor());
            blogForumPostDto.setCreateTime(post.getCreateTime());
            blogForumPostDto.setTestPointId(post.getTestPointId());
            blogForumPostDto.setLikes(post.getLikes());
            blogForumPostDto.setIsLiked(post.getIsLiked());

            BlogComment comment = new BlogComment();
            comment.setPostId(post.getPostId());
            List<BlogComment> comments = blogCommentService.selectBlogCommentList(comment);
            blogForumPostDto.setComments(comments);
//            post.setRemark(comments.size() > 0 ? String.valueOf(comments.size()) : null);
            blogForumPostDtos.add(blogForumPostDto);
        }
        System.out.println(JSONObject.toJSONString(blogForumPostDtos));
        return AjaxResult.success(blogForumPostDtos);
    }

    /**
     * 获取论坛帖子详情
     */
    @GetMapping("/forum-posts/{postId}")
    public AjaxResult getForumPost(@PathVariable("postId") String postId)
    {
        return AjaxResult.success(blogForumPostService.selectBlogForumPostByPostId(postId));
    }

    /**
     * 新增论坛帖子
     */
    @PostMapping("/forumPosts/add")
    public Object addForumPost(@RequestBody BlogForumPost blogForumPost)
    {
        blogForumPost.setAuthor(getUsername());
        blogForumPost.setCreateBy(getUsername());
        blogForumPost.setLikes(0);
        String postId = IdUtils.fastSimpleUUID();
        blogForumPost.setPostId(postId);
        int insert = blogForumPostService.insertBlogForumPost(blogForumPost);
        if (insert > 0) {
            BlogForumPost blogForumPost1 = new BlogForumPost();
            blogForumPost1.setPostId(postId);
            blogForumPost1.setContent(blogForumPost.getContent());
            blogForumPost1.setAuthor(getUsername());
            blogForumPost1.setCreateTime(new Date());
            blogForumPost1.setTestPointId(blogForumPost.getTestPointId());
            blogForumPost1.setLikes(0);
            blogForumPost1.setIsLiked(false);
            return R.ok(JSONObject.toJSONString(blogForumPost1));
        }
        return AjaxResult.error("系统错误,发布失败");
    }

    /**
     * 修改论坛帖子
     */
    @PostMapping("/forumPosts/update")
    public AjaxResult updateForumPost(@RequestBody BlogForumPost blogForumPost)
    {
        blogForumPost.setUpdateBy(getUsername());
        return toAjax(blogForumPostService.updateBlogForumPost(blogForumPost));
    }

    /**
     * 删除论坛帖子
     */
    @DeleteMapping("/forumPosts/{postId}")
    public AjaxResult removeForumPost(@PathVariable String postId)
    {
        return toAjax(blogForumPostService.deleteBlogForumPostByPostId(postId));
    }

    /**
     * 点赞/取消点赞帖子
     */
    @PostMapping("/forumPosts/{postId}/like")
    public AjaxResult likePost(@PathVariable String postId, @RequestBody Map<String, Boolean> requestBody)
    {
        Boolean isLike = requestBody.get("isLike");
        if (isLike == null) {
            return AjaxResult.error("参数错误：缺少isLike字段");
        }

        String userId = SecurityUtils.getUserId().toString();
        BlogForumPost post = blogForumPostService.likePost(postId, userId, isLike);

        return AjaxResult.success(post);
    }

    /**
     * 获取帖子评论列表
     */
    @GetMapping("/comments")
    public AjaxResult getComments(BlogComment blogComment)
    {
        List<BlogComment> list = blogCommentService.selectBlogCommentList(blogComment);
        return AjaxResult.success(list);
    }

    /**
     * 获取评论详情
     */
    @GetMapping("/comments/{commentId}")
    public AjaxResult getComment(@PathVariable("commentId") String commentId)
    {
        return AjaxResult.success(blogCommentService.selectBlogCommentByCommentId(commentId));
    }

    /**
     * 新增评论
     */
    @PostMapping("/comments")
    public AjaxResult addComment(@RequestBody BlogComment blogComment)
    {
        String commentId = IdUtils.fastSimpleUUID();
        blogComment.setCommentId(commentId);
        blogComment.setAuthor(getUsername());
        blogComment.setCreateBy(getUsername());

        BlogComment blogComment1 = new BlogComment();
        blogComment1.setCommentId(commentId);
        blogComment1.setContent(blogComment.getContent());
        blogComment1.setPostId(blogComment.getPostId());
        blogComment1.setAuthor(getUsername());
        blogComment1.setCreateTime(new Date());
        if (blogCommentService.insertBlogComment(blogComment1) > 0) {
            return AjaxResult.success(blogComment1);
        }
        return AjaxResult.error("系统错误,发布失败");
    }

    /**
     * 修改评论
     */
    @PutMapping("/comments")
    public AjaxResult updateComment(@RequestBody BlogComment blogComment)
    {
        blogComment.setUpdateBy(getUsername());
        return toAjax(blogCommentService.updateBlogComment(blogComment));
    }

    /**
     * 删除评论
     */
    @DeleteMapping("/comments/{commentId}")
    public AjaxResult removeComment(@PathVariable String commentId)
    {
        return toAjax(blogCommentService.deleteBlogCommentByCommentId(commentId));
    }
}
