<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.BlogTestPointMapper">

    <resultMap type="BlogTestPoint" id="BlogTestPointResult">
        <result property="testPointId"    column="test_point_id"    />
        <result property="name"    column="name"    />
        <result property="moduleId"    column="module_id"    />
        <result property="isPinned"    column="is_pinned"    />
        <result property="description"    column="description"    />
        <result property="orderNum"    column="order_num"    />
        <result property="status"    column="status"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectMaxTestPointIdVo">
        select max(order_num) from blog_test_point
    </sql>

    <sql id="selectBlogTestPointVo">
        select test_point_id, name, module_id, is_pinned, description, order_num, status, del_flag, create_by, create_time, update_by, update_time, remark from blog_test_point
    </sql>

    <select id="selectBlogTestPointList" parameterType="BlogTestPoint" resultMap="BlogTestPointResult">
        <include refid="selectBlogTestPointVo"/>
        <where>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="moduleId != null  and moduleId != ''"> and module_id = #{moduleId}</if>
            <if test="isPinned != null "> and is_pinned = #{isPinned}</if>
            <if test="description != null  and description != ''"> and description like concat('%', #{description}, '%')</if>
            <if test="orderNum != null "> and order_num = #{orderNum}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            and del_flag = '0'
        </where>
        order by is_pinned desc, order_num
    </select>

    <select id="selectBlogTestPointByTestPointId" parameterType="String" resultMap="BlogTestPointResult">
        <include refid="selectBlogTestPointVo"/>
        where test_point_id = #{testPointId} and del_flag = '0'
    </select>
    <select id="selectBlogTestPointByTestPointIdAndCreateBy" resultMap="BlogTestPointResult">
        <include refid="selectBlogTestPointVo"/>
        where test_point_id = #{testPointId} and create_by =  #{createBy} and del_flag = '0'
    </select>

    <select id="selectMaxTestPointId" parameterType="String" resultType="Integer">
        select max(order_num) from blog_test_point
        where module_id = #{moduleId} and del_flag = '0' and status = '0'
    </select>

    <insert id="insertBlogTestPoint" parameterType="BlogTestPoint">
        insert into blog_test_point
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="testPointId != null">test_point_id,</if>
            <if test="name != null">name,</if>
            <if test="moduleId != null">module_id,</if>
            <if test="isPinned != null">is_pinned,</if>
            <if test="description != null">description,</if>
            <if test="orderNum != null">order_num,</if>
            <if test="status != null">status,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="testPointId != null">#{testPointId},</if>
            <if test="name != null">#{name},</if>
            <if test="moduleId != null">#{moduleId},</if>
            <if test="isPinned != null">#{isPinned},</if>
            <if test="description != null">#{description},</if>
            <if test="orderNum != null">#{orderNum},</if>
            <if test="status != null">#{status},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateBlogTestPoint" parameterType="BlogTestPoint">
        update blog_test_point
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="moduleId != null">module_id = #{moduleId},</if>
            <if test="isPinned != null">is_pinned = #{isPinned},</if>
            <if test="description != null">description = #{description},</if>
            <if test="orderNum != null">order_num = #{orderNum},</if>
            <if test="status != null">status = #{status},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where test_point_id = #{testPointId}
    </update>

    <delete id="deleteBlogTestPointByTestPointId" parameterType="String">
        update blog_test_point set del_flag = '2' where test_point_id = #{testPointId}
    </delete>

    <delete id="deleteBlogTestPointByTestPointIds" parameterType="String">
        update blog_test_point set del_flag = '2' where test_point_id in
        <foreach item="testPointId" collection="array" open="(" separator="," close=")">
            #{testPointId}
        </foreach>
    </delete>
</mapper>
