package com.ruoyi.system.service.impl;

import com.ruoyi.system.domain.BlogTestPoint;
import com.ruoyi.system.mapper.BlogTestPointMapper;
import com.ruoyi.system.service.IBlogTestPointService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 博客测试要点Service业务层处理
 *
 * <AUTHOR>
 */
@Service
public class BlogTestPointServiceImpl implements IBlogTestPointService
{
    @Autowired
    private BlogTestPointMapper blogTestPointMapper;

    /**
     * 查询博客测试要点
     *
     * @param testPointId 博客测试要点主键
     * @return 博客测试要点
     */
    @Override
    public BlogTestPoint selectBlogTestPointByTestPointId(String testPointId)
    {
        return blogTestPointMapper.selectBlogTestPointByTestPointId(testPointId);
    }

    @Override
    public BlogTestPoint selectBlogTestPointByTestPointIdAndCreateBy(String testPointId, String createBy) {
        return blogTestPointMapper.selectBlogTestPointByTestPointIdAndCreateBy(testPointId, createBy);
    }

    /**
     * 查询博客测试要点列表
     *
     * @param blogTestPoint 博客测试要点
     * @return 博客测试要点
     */
    @Override
    public List<BlogTestPoint> selectBlogTestPointList(BlogTestPoint blogTestPoint)
    {
        return blogTestPointMapper.selectBlogTestPointList(blogTestPoint);
    }

    /**
     * 新增博客测试要点
     *
     * @param blogTestPoint 博客测试要点
     * @return 结果
     */
    @Override
    public int insertBlogTestPoint(BlogTestPoint blogTestPoint)
    {
        // 获取最大的显示顺序
        Integer maxOrderNum = blogTestPointMapper.selectMaxTestPointId(blogTestPoint.getModuleId());
        // 判断maxOrderNum是否为null，如果为null则设置为0，否则+1
        blogTestPoint.setOrderNum(maxOrderNum == null ? 1 : maxOrderNum + 1);
        blogTestPoint.setDelFlag("0");
        blogTestPoint.setStatus("0");
        blogTestPoint.setCreateTime(new Date());
        return blogTestPointMapper.insertBlogTestPoint(blogTestPoint);
    }

    /**
     * 修改博客测试要点
     *
     * @param blogTestPoint 博客测试要点
     * @return 结果
     */
    @Override
    public int updateBlogTestPoint(BlogTestPoint blogTestPoint)
    {
        blogTestPoint.setUpdateTime(new Date());
        return blogTestPointMapper.updateBlogTestPoint(blogTestPoint);
    }

    /**
     * 批量删除博客测试要点
     *
     * @param testPointIds 需要删除的博客测试要点主键
     * @return 结果
     */
    @Override
    public int deleteBlogTestPointByTestPointIds(String[] testPointIds)
    {
        return blogTestPointMapper.deleteBlogTestPointByTestPointIds(testPointIds);
    }

    /**
     * 删除博客测试要点信息
     *
     * @param testPointId 博客测试要点主键
     * @return 结果
     */
    @Override
    public int deleteBlogTestPointByTestPointId(String testPointId)
    {
        return blogTestPointMapper.deleteBlogTestPointByTestPointId(testPointId);
    }

    /**
     * 切换测试要点置顶状态
     *
     * @param testPointId 测试要点ID
     * @param isPinned 是否置顶
     * @return 结果
     */
    @Override
    public int toggleTestPointPin(String testPointId, Integer isPinned)
    {
        BlogTestPoint blogTestPoint = new BlogTestPoint();
        blogTestPoint.setTestPointId(testPointId);
        blogTestPoint.setIsPinned(isPinned);
        blogTestPoint.setUpdateTime(new Date());
        return blogTestPointMapper.updateBlogTestPoint(blogTestPoint);
    }
}
