package com.ruoyi.system.mapper;

import com.ruoyi.system.domain.BlogTestPoint;

import java.util.List;

/**
 * 博客测试要点Mapper接口
 *
 * <AUTHOR>
 */
public interface BlogTestPointMapper
{
    /**
     * 查询博客测试要点
     *
     * @param testPointId 博客测试要点主键
     * @return 博客测试要点
     */
    public BlogTestPoint selectBlogTestPointByTestPointId(String testPointId);

    public BlogTestPoint selectBlogTestPointByTestPointIdAndCreateBy(String testPointId, String createBy);

    /**
     * 查询最大的显示顺序
     *
     * @param moduleId
     * @return
     */
    public Integer selectMaxTestPointId(String moduleId);

    /**
     * 查询博客测试要点列表
     *
     * @param blogTestPoint 博客测试要点
     * @return 博客测试要点集合
     */
    public List<BlogTestPoint> selectBlogTestPointList(BlogTestPoint blogTestPoint);

    /**
     * 新增博客测试要点
     *
     * @param blogTestPoint 博客测试要点
     * @return 结果
     */
    public int insertBlogTestPoint(BlogTestPoint blogTestPoint);

    /**
     * 修改博客测试要点
     *
     * @param blogTestPoint 博客测试要点
     * @return 结果
     */
    public int updateBlogTestPoint(BlogTestPoint blogTestPoint);

    /**
     * 删除博客测试要点
     *
     * @param testPointId 博客测试要点主键
     * @return 结果
     */
    public int deleteBlogTestPointByTestPointId(String testPointId);

    /**
     * 批量删除博客测试要点
     *
     * @param testPointIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteBlogTestPointByTestPointIds(String[] testPointIds);
}
